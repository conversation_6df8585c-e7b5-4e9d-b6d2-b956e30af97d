# 环境配置指南

## 1. 环境变量配置

请在项目根目录创建 `.env` 文件，并添加以下配置：

```env
# 应用基本配置
VITE_APP_TITLE=Glowxq-Nexus

# 服务端配置 - 请根据实际后端地址修改
VITE_SERVER_BASEURL=http://localhost:8080
VITE_SERVER_PORT=3000

# H5代理配置
VITE_APP_PROXY=false
VITE_APP_PROXY_PREFIX=/api

# 上传图片地址 - 请根据实际地址修改
VITE_UPLOAD_BASEURL=http://localhost:8080

# 是否清除console
VITE_DELETE_CONSOLE=false

# 客户端ID - 与后端配置保持一致
VITE_APP_CLIENT_ID=web
```

## 2. 问题修复说明

### 2.1 C105 错误循环调用问题

**问题描述：** `/tenant/enable/status` 接口返回 C105 错误码并循环调用

**修复方案：**

1. 在请求拦截器中添加了无需认证的接口列表
2. 延迟调用租户状态接口，避免在页面初始化时立即调用
3. 修改了错误处理逻辑，避免循环调用

**涉及文件：**

- `src/interceptors/request.ts` - 添加了无需认证接口白名单
- `src/pages/rescue/login/login.vue` - 延迟调用租户接口

### 2.2 登录表单显示问题

**问题描述：** 原来使用的 `uni-easyinput` 组件无法正常显示

**修复方案：**

1. 改用原生 `<input>` 元素
2. 简化了组件依赖
3. 保持了良好的用户体验

### 2.3 ESLint 校验问题

**已修复的问题：**

1. 移除了 `qs` 模块依赖，使用自定义序列化函数
2. 修复了环境变量引用问题
3. 修复了类型比较问题
4. 统一了代码格式

## 3. 使用方法

### 3.1 启动项目

```bash
# 安装依赖
pnpm install

# 启动开发环境
pnpm dev

# H5 开发
pnpm dev:h5

# 微信小程序开发
pnpm dev:mp-weixin
```

### 3.2 登录功能测试

1. 确保后端服务已启动
2. 访问登录页面：`/pages/rescue/login/login`
3. 使用测试账号：
   - 用户名：`13667753053`
   - 密码：`123456`

### 3.3 注册功能测试

1. 从登录页面点击"立即注册"
2. 填写注册信息
3. 提交注册表单

## 4. 注意事项

### 4.1 后端接口配置

确保以下接口无需认证即可访问：

- `/tenant/enable/status`
- `/auth/login`
- `/client/sys-user/auth/register`
- `/captcha/status`
- `/captcha/get`
- `/captcha/check`

### 4.2 租户功能

如果启用了租户功能，请确保：

1. 租户代码正确配置
2. 租户ID正确传递
3. 后端支持多租户

### 4.3 错误处理

项目已配置统一的错误处理：

- C105：Token失效，自动跳转登录页
- 网络错误：显示友好提示
- 业务错误：显示具体错误信息

## 5. 开发建议

1. **环境变量管理**：根据不同环境（开发、测试、生产）配置不同的 `.env` 文件
2. **接口调试**：使用浏览器开发者工具或 Postman 测试接口
3. **错误日志**：关注控制台输出，及时发现和解决问题
4. **代码规范**：运行 `pnpm lint:fix` 修复代码格式问题

## 6. 常见问题

### Q: 为什么登录后跳转失败？

A: 请检查 `pages.json` 中是否正确配置了首页路径

### Q: 验证码功能无法使用？

A: 确保后端验证码服务正常，并且接口地址正确

### Q: 租户功能如何配置？

A: 在环境变量中配置租户信息，或在登录时动态设置

### Q: ESLint 错误如何修复？

A: 运行 `pnpm lint:fix` 自动修复大部分格式问题

## 7. 技术支持

如遇到问题，请：

1. 查看控制台错误日志
2. 检查网络请求状态
3. 确认环境变量配置
4. 联系开发团队获取支持
