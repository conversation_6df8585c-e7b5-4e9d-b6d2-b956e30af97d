# Glowxq-App 登录注册功能实现说明

本文档说明了如何在 glowxq-app 项目中实现登录和注册功能，参考了 glowxq-web 项目的设计。

## 实现的功能

### 1. API 结构

#### 辅助工具
- `src/api/helper/prefix.ts` - API 前缀配置
- `src/api/helper/index.ts` - 状态码定义和错误处理

#### 接口定义
- `src/api/interface/system/admin/login.ts` - 登录相关类型定义
  - ILogin 命名空间：登录、注册参数和响应类型
  - ICaptcha 命名空间：验证码相关类型
  - ITenant 命名空间：租户相关类型

#### API 模块
- `src/api/modules/system/admin/login.ts` - 登录注册 API
- `src/api/modules/system/admin/captcha.ts` - 验证码 API
- `src/api/modules/system/tenant/tenant.ts` - 租户 API

### 2. 页面实现

#### 登录页面
- 路径：`pages/rescue/login/login.vue`
- 功能：
  - 用户名密码登录
  - 滑块验证码支持
  - 租户功能支持
  - 自动菜单类型管理
  - 响应式设计适配移动端

#### 注册页面
- 路径：`pages/rescue/register/register.vue`
- 功能：
  - 用户注册表单
  - 表单验证（用户名、密码、邮箱、手机号等）
  - 租户代码支持
  - 注册码支持

#### 验证码组件
- 路径：`src/components/slider-captcha.vue`
- 功能：
  - 滑块验证码实现
  - 触摸拖拽支持
  - 验证状态反馈

### 3. 状态管理

#### 用户 Store 扩展
- 文件：`src/store/user.ts`
- 新增方法：
  - `loginWithNewApi()` - 新版登录 API
  - `logoutWithNewApi()` - 新版退出 API
  - 兼容 glowxq-web 的数据结构

### 4. 请求拦截器增强

#### 请求头支持
- Authorization: Bearer Token
- MenuType: 菜单类型
- Tenant-Id: 租户ID
- Platform: 平台标识

#### 错误处理
- 统一错误码处理
- 登录失效自动跳转
- 网络错误友好提示

### 5. 环境配置

#### 新增环境变量
- `VITE_APP_CLIENT_ID` - 客户端ID

#### 类型定义
- 扩展 `ImportMetaEnv` 接口
- 添加 `Menu` 命名空间

## 使用方法

### 1. 环境配置

在项目的环境配置文件中添加：

```env
# 客户端ID
VITE_APP_CLIENT_ID=web
```

### 2. 页面路由

在 `pages.json` 中已配置登录和注册页面路由：

```json
{
  "path": "pages/rescue/login/login",
  "type": "page",
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "登录"
  }
},
{
  "path": "pages/rescue/register/register",
  "type": "page",
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "注册"
  }
}
```

### 3. 在页面中使用

#### 登录功能
```typescript
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 使用新的登录API
await userStore.loginWithNewApi({
  username: 'your_username',
  password: 'your_password',
  clientId: 'web',
  grantType: 'password'
})
```

#### 注册功能
```typescript
import { registerApi } from '@/api/modules/system/admin/login'

await registerApi({
  username: 'new_username',
  password: 'new_password',
  tenantKey: 'tenant_code'
})
```

### 4. 验证码使用

```vue
<template>
  <slider-captcha
    ref="captchaRef"
    @success="onCaptchaSuccess"
    @close="onCaptchaClose"
  />
</template>

<script setup>
const captchaRef = ref()

// 显示验证码
const showCaptcha = () => {
  captchaRef.value?.show()
}
</script>
```

## 技术栈

- **框架**: UniApp + Vue3 + TypeScript
- **脚手架**: Unibest
- **状态管理**: Pinia
- **UI组件**: uni-ui
- **网络请求**: 基于 uni.request 封装

## 与 glowxq-web 的兼容性

本实现完全兼容 glowxq-web 的后端 API，包括：

1. **相同的API端点**：使用相同的登录、注册、验证码接口
2. **相同的数据结构**：请求和响应参数保持一致
3. **相同的业务逻辑**：租户、权限、菜单类型等功能一致
4. **相同的错误处理**：状态码和错误信息处理方式相同

## 注意事项

1. **依赖安装**：确保安装了必要的 uni-ui 组件
2. **环境变量**：正确配置客户端ID等环境变量
3. **网络配置**：确保后端API地址配置正确
4. **权限管理**：根据实际需要配置页面访问权限

## 后续扩展

可以根据实际需求扩展以下功能：

1. **微信登录**：集成微信小程序登录
2. **短信验证**：添加短信验证码登录
3. **生物识别**：支持指纹、面容识别
4. **多语言**：国际化支持
5. **主题切换**：深色模式支持 
