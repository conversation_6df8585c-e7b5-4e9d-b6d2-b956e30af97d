<script setup lang="ts">
const selectedStatus = ref('')
const showPicker = ref(false)

const statusOptions = [
  { code: '1', name: '启用' },
  { code: '2', name: '禁用' },
  { code: '3', name: '待审核' },
]

function selectStatus(item: any) {
  selectedStatus.value = item.name
  showPicker.value = false
}
</script>

<template>
  <view class="enum-test">
    <view class="test-section">
      <view class="section-title">EnumShow 测试</view>
      <view class="test-item">
        <view>状态1:</view>
        <view class="enum-display">
          <view class="enum-text">启用</view>
        </view>
      </view>
      <view class="test-item">
        <view>状态2:</view>
        <view class="enum-display enum-display-success">
          <view class="enum-text">成功</view>
        </view>
      </view>
      <view class="test-item">
        <view>状态3:</view>
        <view class="enum-display enum-display-error">
          <view class="enum-text">错误</view>
        </view>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">EnumSelect 测试</view>
      <view class="test-item">
        <view>选择状态:</view>
        <view class="select-demo" @click="showPicker = true">
          <view>{{ selectedStatus || '请选择' }}</view>
          <view class="arrow">▼</view>
        </view>
      </view>

      <!-- 模拟选择器弹窗 -->
      <wd-popup
        v-model="showPicker"
        position="bottom"
        :root-portal="true"
        :z-index="99999"
        :lock-scroll="true"
      >
        <view class="picker-header">
          <view>请选择状态</view>
          <view @click="showPicker = false">完成</view>
        </view>
        <view class="picker-content">
          <view
            v-for="item in statusOptions"
            :key="item.code"
            class="picker-item"
            @click="selectStatus(item)"
          >
            <view>{{ item.name }}</view>
          </view>
        </view>
      </wd-popup>
    </view>
  </view>
</template>

<style scoped lang="scss">
.enum-test {
  padding: 40rpx;

  .test-section {
    margin-bottom: 60rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 32rpx;
      display: block;
    }

    .test-item {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;

      .enum-display {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8rpx 24rpx;
        border-radius: 12rpx;
        background-color: #f5f5f5;
        color: #666;
        font-size: 28rpx;
        min-height: 64rpx;

        &.enum-display-success {
          background-color: #f6ffed;
          color: #52c41a;
        }

        &.enum-display-error {
          background-color: #fff2f0;
          color: #ff4d4f;
        }

        .enum-text {
          font-size: 28rpx;
        }
      }

      .select-demo {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        background-color: #fff;
        border: 2rpx solid #e0e0e0;
        border-radius: 16rpx;
        min-height: 96rpx;
        flex: 1;
        margin-left: 16rpx;

        .arrow {
          color: #999;
          font-size: 24rpx;
        }
      }
    }
  }
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
}

.picker-content {
  max-height: 60vh;

  .picker-item {
    padding: 32rpx;
    border-bottom: 2rpx solid #f0f0f0;
    font-size: 28rpx;

    &:active {
      background-color: #f5f5f5;
    }
  }
}
</style>
