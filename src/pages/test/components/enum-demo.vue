<script setup lang="ts">
import type { BaseEnum } from '@/enums/base'
import EnumSelect from '@/components/common/enums/EnumSelect.vue'
import EnumShow from '@/components/common/enums/EnumShow.vue'

// 状态枚举示例
const statusEnum: Record<string, BaseEnum> = {
  DISABLED: {
    code: '0',
    name: '禁用',
    description: '系统已禁用',
    tooltip: '当前状态为禁用',
  },
  ENABLED: {
    code: '1',
    name: '启用',
    description: '系统已启用',
    tooltip: '当前状态为启用',
  },
  PENDING: {
    code: '2',
    name: '待审核',
    description: '等待审核中',
    tooltip: '当前状态为待审核',
  },
  REJECTED: {
    code: '3',
    name: '已拒绝',
    description: '审核被拒绝',
    tooltip: '当前状态为已拒绝',
  },
}

// 单选状态
const selectedStatus = ref('1')
const selectedStatus2 = ref('2')
const selectedStatus3 = ref('1')
const selectedStatus4 = ref('1')

// 多选状态
const selectedStatuses = ref(['1', '2'])

// 事件处理
function handleStatusChange(value: any) {
  console.log('状态变化:', value)
}

function handleStatusChange2(value: any) {
  console.log('状态变化2:', value)
}

function handleStatusChange3(value: any) {
  console.log('状态变化3:', value)
}

function handleStatusChange4(value: any) {
  console.log('状态变化4:', value)
}

function handleMultiStatusChange(value: any) {
  console.log('多选状态变化:', value)
}
</script>

<template>
  <view class="enum-demo">
    <view class="demo-section">
      <view class="section-title">EnumShow 组件示例</view>

      <view class="demo-item">
        <view class="demo-label">基础显示：</view>
        <EnumShow :enum="statusEnum" code="1" />
      </view>

      <view class="demo-item">
        <view class="demo-label">自定义样式：</view>
        <EnumShow
          :enum="statusEnum"
          code="2"
          background-color="#e6f7ff"
          color="#1890ff"
          border-color="#91d5ff"
        />
      </view>

      <view class="demo-item">
        <view class="demo-label">错误状态：</view>
        <EnumShow
          :enum="statusEnum"
          code="0"
          background-color="#fff2f0"
          color="#ff4d4f"
          border-color="#ffccc7"
        />
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">EnumSelect 组件示例</view>

      <view class="demo-item">
        <view class="demo-label">移动端选择器：</view>
        <EnumSelect
          v-model="selectedStatus"
          :enum-data="statusEnum"
          type="mobile"
          placeholder="请选择状态"
          @change="handleStatusChange"
        />
        <view class="demo-value">选中值: {{ selectedStatus }}</view>
      </view>

      <view class="demo-item">
        <view class="demo-label">Tab风格：</view>
        <EnumSelect
          v-model="selectedStatus2"
          :enum-data="statusEnum"
          type="tab"
          @change="handleStatusChange2"
        />
        <view class="demo-value">选中值: {{ selectedStatus2 }}</view>
      </view>

      <view class="demo-item">
        <view class="demo-label">单选按钮：</view>
        <EnumSelect
          v-model="selectedStatus3"
          :enum-data="statusEnum"
          type="radio"
          @change="handleStatusChange3"
        />
        <view class="demo-value">选中值: {{ selectedStatus3 }}</view>
      </view>

      <view class="demo-item">
        <view class="demo-label">多选模式：</view>
        <EnumSelect
          v-model="selectedStatuses"
          :enum-data="statusEnum"
          type="mobile"
          :multiple="true"
          placeholder="请选择多个状态"
          @change="handleMultiStatusChange"
        />
        <view class="demo-value">选中值: {{ selectedStatuses }}</view>
      </view>

      <view class="demo-item">
        <view class="demo-label">禁用某些选项：</view>
        <EnumSelect
          v-model="selectedStatus4"
          :enum-data="statusEnum"
          type="tab"
          :disabled-values="['0']"
          @change="handleStatusChange4"
        />
        <view class="demo-value">选中值: {{ selectedStatus4 }}</view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.enum-demo {
  padding: 40rpx;

  .demo-section {
    margin-bottom: 60rpx;

    .section-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 32rpx;
      display: block;
    }

    .demo-item {
      margin-bottom: 40rpx;
      padding: 32rpx;
      background-color: #f8f9fa;
      border-radius: 16rpx;

      .demo-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
        display: block;
      }

      .demo-value {
        font-size: 24rpx;
        color: #999;
        margin-top: 16rpx;
        display: block;
        background-color: #fff;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        border: 2rpx solid #e0e0e0;
      }
    }
  }
}
</style>
