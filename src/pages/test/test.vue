<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '组件测试',
  },
}
</route>

<script lang="ts" setup>
import EnumSelect from '@/components/common/enums/EnumSelect.vue'
import TagSelect from '@/components/common/tag/TagSelect.vue'
import UploadImage from '@/components/common/upload/UploadImage.vue'
import { BloodType } from '@/enums/rescue'
import TextShow from '@/components/common/meta/TextShow.vue'

const value = ref('')
const value2 = ref([])
const value3 = ref()

const columns = ref(['选项1', '选项2', '选项3', '选项4', '选项5', '选项6', '选项7'])
// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 奇怪：同样的代码放在 vue 里面不会校验到错误，放在 .ts 文件里面会校验到错误
// const testOxlint = (name: string) => {
//   console.log('oxlint')
// }
// testOxlint('oxlint')
console.log('about')
</script>

<template>
  <view class="p-40rpx">
    <TagSelect />
    <wd-divider />
    <UploadImage />
    <wd-divider />
    <EnumSelect v-model="value" :enum-data="BloodType" type="tab" searchable />
    <enum-show border-color="#1890ff" background-color="#e6f7ff" :enum="BloodType" :code="value" />
    <wd-divider />
    <warehouse-select v-model="value2" />
    <wd-tag bg-color="#1890ff">仓库ID：{{ value2 }}</wd-tag>
    <wd-divider />
    <DeptSelect v-model="value3" />
    <wd-tag bg-color="#1890ff">部门ID：{{ value3 }}</wd-tag>

    <navigator
      url="/pages/demo/dept-select-demo"
      open-type="navigate"
      hover-class="navigator-hover"
    >
      <wd-button type="primary">跳转dept</wd-button>
    </navigator>
    <wd-divider />
    <navigator url="/pages/demo/sign" open-type="navigate" hover-class="navigator-hover">
      <wd-button type="primary">跳转签名</wd-button>
    </navigator>
    <wd-divider />
    <DrawSign />
    <wd-divider />
    <!-- 使用TextShow组件展示帮助内容 -->
    <TextShow
      text-key="privacy-policy"
    />
  </view>
</template>

<style lang="scss" scoped>
.test-css {
  // 16rpx=>0.5rem
  padding-bottom: 16rpx;
  // mt-4=>1rem=>32rpx;
  margin-top: 32rpx;
  text-align: center;
}
</style>
