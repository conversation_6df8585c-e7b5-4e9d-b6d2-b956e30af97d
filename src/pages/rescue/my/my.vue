<script setup lang="ts">
import { getMyDetailApi } from '@/api/modules/rescue/user/user'
import type { IRescueUser } from '@/api/interface/rescue/user/user'

// 页面状态
const pageLoaded = ref(false)
const loading = ref(false)

// 用户信息
const userInfo = ref<IRescueUser.UserInfoVO | null>(null)

// 问候语
const greetingTime = ref('')
const greetingMessage = ref('')

// 获取问候语
function getGreeting() {
  const hour = new Date().getHours()
  if (hour < 9) {
    greetingTime.value = '早上好'
    greetingMessage.value = '新的一天开始了'
  } else if (hour < 12) {
    greetingTime.value = '上午好'
    greetingMessage.value = '工作顺利'
  } else if (hour < 18) {
    greetingTime.value = '下午好'
    greetingMessage.value = '继续加油'
  } else {
    greetingTime.value = '晚上好'
    greetingMessage.value = '辛苦了一天'
  }
}

// 功能入口配置
const functionEntries = ref([
  { id: 'profile', title: '个人资料', subtitle: '查看和编辑个人信息', icon: 'user', color: '#007AFF' },
  { id: 'my-apply', title: '我的申请', subtitle: '查看我的物资申请记录', icon: 'list', color: '#34C759' },
  { id: 'review', title: '待审批物资', subtitle: '审批物资申请', icon: 'check-circle', color: '#FF9500' },
  { id: 'help', title: '帮助中心', subtitle: '常见问题和帮助', icon: 'help-circle', color: '#34C759' },
  { id: 'about', title: '关于我们', subtitle: '应用信息和版本', icon: 'info-circle', color: '#FF9500' },
  { id: 'settings', title: '设置', subtitle: '应用设置和偏好', icon: 'setting', color: '#8E8E93' },
])

// 导航到功能页面
function navigateToFunction(entry: any) {
  const routes: Record<string, string> = {
    profile: '/pages/rescue/my/my-detail',
    'my-apply': '/pages/rescue/materials/my-apply',
    review: '/pages/rescue/materials/review',
    help: '/pages/rescue/my/help',
    about: '/pages/rescue/my/about'
  }

  if (routes[entry.id]) {
    uni.navigateTo({ url: routes[entry.id] })
  } else {
    uni.showToast({ title: `${entry.title}功能开发中`, icon: 'none' })
  }
}

// 退出登录
function handleLogout() {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        uni.reLaunch({ url: '/pages/rescue/login/login' })
      }
    },
  })
}

// 获取用户详情
async function fetchUserDetail() {
  try {
    loading.value = true
    const response = await getMyDetailApi()
    if (response.data) {
      userInfo.value = response.data
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getGreeting()
  pageLoaded.value = true
  fetchUserDetail()
})
</script>

<template>
  <view class="my-page" :class="{ loaded: pageLoaded }">
    <!-- 顶部问候 -->
    <view class="greeting-section">
      <view class="greeting-content">
        <view class="greeting-time">{{ greetingTime }}</view>
        <view class="greeting-message">{{ greetingMessage }}</view>
      </view>
      <view class="weather-info">
        <wd-icon name="sun" size="28rpx" color="#666" />
        <text class="weather-text">晴朗</text>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-content">
        <view class="avatar-section">
          <image
            :src="userInfo?.avatar || '/static/images/default-avatar.png'"
            class="user-avatar"
            mode="aspectFill"
          />
          <view class="status-dot" />
        </view>
        <view class="user-info">
          <view class="user-name">
            {{ userInfo?.name || userInfo?.nickName || '未设置姓名' }}
          </view>
          <view class="user-subtitle">
            {{ userInfo?.post || '用户' }}
            <text v-if="userInfo?.numberPrefix && userInfo?.number">
              · {{ userInfo.numberPrefix }}{{ userInfo.number }}
            </text>
          </view>
          <view v-if="userInfo?.roles?.length" class="user-roles">
            <view v-for="role in userInfo.roles" :key="role.id" class="role-tag">
              {{ role.roleName }}
            </view>
          </view>
        </view>
        <view class="edit-btn" @tap="navigateToFunction({ id: 'profile' })">
          <wd-icon name="edit" size="32rpx" color="#007AFF" />
        </view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="function-list">
      <view
        v-for="entry in functionEntries"
        :key="entry.id"
        class="function-item"
        @tap="navigateToFunction(entry)"
      >
        <view class="item-icon">
          <wd-icon :name="entry.icon" size="40rpx" :color="entry.color" />
        </view>
        <view class="item-content">
          <view class="item-title">{{ entry.title }}</view>
          <view class="item-subtitle">{{ entry.subtitle }}</view>
        </view>
        <view class="item-arrow">
          <wd-icon name="arrow-right" size="32rpx" color="#C7C7CC" />
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <view class="logout-btn" @tap="handleLogout">
        <wd-icon name="logout" size="32rpx" color="#FF3B30" />
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 安全区域 -->
    <view class="safe-area" />
  </view>
</template>

<style lang="scss" scoped>
$primary-color: #007AFF;
$text-primary: #000000;
$text-secondary: rgba(60, 60, 67, 0.6);
$bg-primary: #ffffff;
$bg-secondary: #f2f2f7;

.my-page {
  min-height: 100vh;
  background: $bg-secondary;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.6s ease;

  &.loaded {
    opacity: 1;
    transform: translateY(0);
  }
}

.greeting-section {
  padding: 60rpx 40rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .greeting-content {
    .greeting-time {
      font-size: 32rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 6rpx;
    }

    .greeting-message {
      font-size: 22rpx;
      color: $text-secondary;
    }
  }

  .weather-info {
    display: flex;
    align-items: center;
    gap: 8rpx;
    background: rgba(0, 0, 0, 0.04);
    padding: 10rpx 16rpx;
    border-radius: 16rpx;

    .weather-text {
      font-size: 20rpx;
      color: $text-secondary;
    }
  }
}

.user-card {
  margin: 0 40rpx 32rpx;
  background: $bg-primary;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

  .user-content {
    padding: 40rpx;
    display: flex;
    align-items: center;
    gap: 32rpx;
  }

  .avatar-section {
    position: relative;

    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      border: 2rpx solid rgba(0, 0, 0, 0.06);
    }

    .status-dot {
      position: absolute;
      bottom: 4rpx;
      right: 4rpx;
      width: 20rpx;
      height: 20rpx;
      background: #34C759;
      border: 2rpx solid #ffffff;
      border-radius: 10rpx;
    }
  }

  .user-info {
    flex: 1;

    .user-name {
      font-size: 32rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 8rpx;
    }

    .user-subtitle {
      font-size: 24rpx;
      color: $text-secondary;
      margin-bottom: 16rpx;
    }

    .user-roles {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .role-tag {
        padding: 6rpx 16rpx;
        background: rgba(0, 122, 255, 0.06);
        border: 1rpx solid rgba(0, 122, 255, 0.12);
        border-radius: 16rpx;
        font-size: 20rpx;
        color: $primary-color;
        font-weight: 500;
      }
    }
  }

  .edit-btn {
    width: 48rpx;
    height: 48rpx;
    background: rgba(0, 122, 255, 0.08);
    border: 1rpx solid rgba(0, 122, 255, 0.15);
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:active {
      background: rgba(0, 122, 255, 0.15);
      transform: scale(0.95);
    }
  }
}

.function-list {
  margin: 0 40rpx 32rpx;
  background: $bg-primary;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .item-icon {
    width: 72rpx;
    height: 72rpx;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 18rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 32rpx;
  }

  .item-content {
    flex: 1;

    .item-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 8rpx;
    }

    .item-subtitle {
      font-size: 26rpx;
      color: $text-secondary;
    }
  }

  .item-arrow {
    margin-left: 16rpx;
  }
}

.logout-section {
  margin: 0 40rpx 32rpx;

  .logout-btn {
    background: $bg-primary;
    border-radius: 20rpx;
    border: 1rpx solid rgba(255, 59, 48, 0.12);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    transition: all 0.2s ease;

    &:active {
      background: rgba(255, 59, 48, 0.02);
    }

    .logout-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #FF3B30;
    }
  }
}

.safe-area {
  height: 40rpx;
}
</style>
