<script setup lang="ts">
import TextShow from '@/components/common/meta/TextShow.vue'

// 页面状态
const pageLoaded = ref(false)
const activeCategory = ref('common')

// 帮助分类
const helpCategories = [
  {
    id: 'common',
    name: '常见问题',
    icon: 'help-circle'
  },
  {
    id: 'account',
    name: '账号相关',
    icon: 'user'
  },
  {
    id: 'materials',
    name: '物资管理',
    icon: 'package'
  },
  {
    id: 'emergency',
    name: '应急处理',
    icon: 'alert-triangle'
  }
]

// 帮助内容配置
const helpContents = {
  common: [
    {
      key: 'how-to-use',
      title: '如何使用应用',
      icon: 'book-open'
    },
    {
      key: 'feature-intro',
      title: '功能介绍',
      icon: 'grid'
    },
    {
      key: 'quick-start',
      title: '快速入门',
      icon: 'play-circle'
    }
  ],
  account: [
    {
      key: 'login-help',
      title: '登录问题',
      icon: 'log-in'
    },
    {
      key: 'profile-edit',
      title: '个人资料修改',
      icon: 'edit'
    },
    {
      key: 'password-reset',
      title: '密码重置',
      icon: 'key'
    }
  ],
  materials: [
    {
      key: 'material-apply',
      title: '物资申请流程',
      icon: 'file-plus'
    },
    {
      key: 'material-review',
      title: '审批操作指南',
      icon: 'check-circle'
    },
    {
      key: 'material-track',
      title: '物资追踪',
      icon: 'map-pin'
    }
  ],
  emergency: [
    {
      key: 'emergency-report',
      title: '紧急情况上报',
      icon: 'phone-call'
    },
    {
      key: 'emergency-response',
      title: '应急响应流程',
      icon: 'zap'
    },
    {
      key: 'safety-guide',
      title: '安全操作指南',
      icon: 'shield'
    }
  ]
}

// 切换分类
function switchCategory(categoryId: string) {
  activeCategory.value = categoryId
}

// 处理文本加载错误
function handleTextError(error: any) {
  console.error('帮助文本加载失败:', error)
}

// 联系客服
function contactService() {
  // #ifdef MP-WEIXIN
  // 微信小程序使用客服消息功能
  uni.openCustomerServiceChat({
    extInfo: {
      url: 'https://work.weixin.qq.com/kfid/kfc123456789' // 这里需要替换为实际的客服链接
    },
    corpId: 'your-corp-id', // 企业微信的corpId，如果使用企业微信客服
    success: (res) => {
      console.log('打开客服会话成功', res)
    },
    fail: (err) => {
      console.error('打开客服会话失败', err)
      // 降级处理：显示客服联系方式
      showContactInfo()
    }
  })
  // #endif

  // #ifndef MP-WEIXIN
  // 非微信小程序环境的处理
  showContactInfo()
  // #endif
}

// 显示客服联系信息
function showContactInfo() {
  uni.showModal({
    title: '联系客服',
    content: '客服电话：************\n工作时间：9:00-18:00\n或发送邮件至：<EMAIL>',
    showCancel: true,
    cancelText: '取消',
    confirmText: '拨打电话',
    success: (res) => {
      if (res.confirm) {
        // 拨打客服电话
        uni.makePhoneCall({
          phoneNumber: '************',
          fail: (err) => {
            console.error('拨打电话失败', err)
            uni.showToast({
              title: '拨打失败，请手动拨打',
              icon: 'none'
            })
          }
        })
      }
    }
  })
}

// 处理微信小程序客服会话事件
function handleContact(event: any) {
  console.log('客服会话事件:', event)
  // 可以在这里记录用户联系客服的行为
  uni.showToast({
    title: '正在为您接入客服...',
    icon: 'loading',
    duration: 1500
  })
}

// 处理获取用户信息事件（如果需要）
function handleGetUserInfo(event: any) {
  console.log('获取用户信息:', event)
}

// 页面加载动画
function startPageAnimation() {
  pageLoaded.value = true
}

onMounted(() => {
  startPageAnimation()

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: '帮助中心'
  })
})
</script>

<template>
  <view class="help-page" :class="{ loaded: pageLoaded }">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-title">帮助中心</view>
        <view class="header-subtitle">Help Center</view>
      </view>
      <view class="header-decoration">
        <wd-icon name="help-circle" size="80rpx" color="rgba(255,255,255,0.2)" />
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs">
      <scroll-view class="tabs-scroll" scroll-x enhanced>
        <view class="tabs-container">
          <view
            v-for="category in helpCategories"
            :key="category.id"
            class="tab-item"
            :class="{ active: activeCategory === category.id }"
            @tap="switchCategory(category.id)"
          >
            <wd-icon :name="category.icon" size="28rpx" />
            <text class="tab-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y enhanced>
      <view class="help-content">
        <view
          v-for="(content, index) in helpContents[activeCategory]"
          :key="content.key"
          class="help-item"
          :style="{ 'animation-delay': `${index * 0.1}s` }"
        >
          <view class="item-header">
            <view class="item-icon">
              <wd-icon :name="content.icon" size="32rpx" color="#34C759" />
            </view>
            <text class="item-title">{{ content.title }}</text>
          </view>

          <!-- 使用TextShow组件展示帮助内容 -->
          <TextShow
            :text-key="content.key"
            custom-class="help-text-show"
            @error="handleTextError"
          />
        </view>
      </view>

      <!-- 联系客服 -->
      <view class="contact-section">
        <view class="contact-card">
          <view class="contact-header">
            <wd-icon name="headphones" size="40rpx" color="#007AFF" />
            <view class="contact-title">需要更多帮助？</view>
          </view>
          <view class="contact-content">
            <text class="contact-text">如果您在使用过程中遇到其他问题，请联系我们的客服团队</text>
          </view>
          <view class="contact-actions">
            <!-- 微信小程序客服按钮 -->
            <!-- #ifdef MP-WEIXIN -->
            <button
              class="contact-btn wx-contact-btn"
              open-type="contact"
              @contact="handleContact"
              @getuserinfo="handleGetUserInfo"
            >
              <wd-icon name="headphones" size="28rpx" color="#ffffff" />
              <text>联系客服</text>
            </button>
            <!-- #endif -->

            <!-- 非微信小程序环境 -->
            <!-- #ifndef MP-WEIXIN -->
            <view class="contact-btn" @tap="contactService">
              <wd-icon name="phone" size="28rpx" color="#ffffff" />
              <text>联系客服</text>
            </view>
            <!-- #endif -->
          </view>
        </view>
      </view>

      <!-- 安全区域 -->
      <view class="safe-area-bottom" />
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统颜色
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray4: #d1d1d6;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;

// 文本颜色
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$label-tertiary: rgba(60, 60, 67, 0.3);

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f2f2f7;

.help-page {
  min-height: 100vh;
  background: $background-secondary;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);

  &.loaded {
    opacity: 1;
    transform: translateY(0);
  }
}

// 页面头部
.page-header {
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .header-content {
    flex: 1;

    .header-title {
      font-size: 48rpx;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 8rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }

    .header-subtitle {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
      letter-spacing: 1rpx;
    }
  }

  .header-decoration {
    opacity: 0.3;
  }
}

// 分类标签
.category-tabs {
  background: $background-primary;
  padding: 24rpx 0;
  border-bottom: 1rpx solid $system-gray6;

  .tabs-scroll {
    white-space: nowrap;
  }

  .tabs-container {
    display: flex;
    padding: 0 40rpx;
    gap: 24rpx;
  }

  .tab-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx 24rpx;
    background: $system-gray6;
    border-radius: 20rpx;
    transition: all 0.3s ease;
    white-space: nowrap;

    .tab-text {
      font-size: 26rpx;
      color: $label-secondary;
      font-weight: 500;
    }

    &.active {
      background: $system-green;

      .tab-text {
        color: #ffffff;
      }

      :deep(.wd-icon) {
        color: #ffffff !important;
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 内容区域
.content-area {
  flex: 1;
  height: calc(100vh - 280rpx);
}

.help-content {
  padding: 40rpx;
}

.help-item {
  margin-bottom: 32rpx;
  opacity: 0;
  transform: translateY(20rpx);
  animation: slideInUp 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;

  .item-header {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 20rpx;
    padding: 0 8rpx;

    .item-icon {
      width: 48rpx;
      height: 48rpx;
      background: rgba(52, 199, 89, 0.1);
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .item-title {
      font-size: 30rpx;
      font-weight: 600;
      color: $label-primary;
      letter-spacing: -0.5rpx;
    }
  }
}

// TextShow组件自定义样式
:deep(.help-text-show) {
  .announcement-container,
  .information-container,
  .default-container {
    background: $background-primary;
    border-radius: 16rpx;
    padding: 28rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
  }

  .agreement-container {
    background: $background-primary;
    border-radius: 16rpx;
    padding: 0;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(255, 255, 255, 0.8);

    .agreement-item {
      padding: 28rpx;
      border-bottom: none;
    }
  }

  .cell-container {
    .cell-item {
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    }
  }
}

// 联系客服
.contact-section {
  padding: 0 40rpx 40rpx;

  .contact-card {
    background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
    border-radius: 24rpx;
    padding: 40rpx 32rpx;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: breathe 10s infinite ease-in-out;
    }

    .contact-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 20rpx;
      position: relative;
      z-index: 2;

      .contact-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
      }
    }

    .contact-content {
      margin-bottom: 32rpx;
      position: relative;
      z-index: 2;

      .contact-text {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
      }
    }

    .contact-actions {
      position: relative;
      z-index: 2;

      .contact-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;
        background: rgba(255, 255, 255, 0.2);
        border: 1rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 20rpx;
        padding: 20rpx 32rpx;
        backdrop-filter: blur(20rpx);
        -webkit-backdrop-filter: blur(20rpx);
        transition: all 0.3s ease;

        text {
          font-size: 28rpx;
          color: #ffffff;
          font-weight: 600;
        }

        &:active {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(0.95);
        }
      }

      // 微信小程序客服按钮特殊样式
      .wx-contact-btn {
        // 重置button默认样式
        border: 1rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 0.2);
        color: #ffffff;
        font-size: 28rpx;
        line-height: normal;

        // 移除button默认样式
        &::after {
          border: none;
        }

        // 确保样式一致性
        &:not([size='mini']) {
          padding: 20rpx 32rpx;
        }
      }
    }
  }
}

// 安全区域
.safe-area-bottom {
  height: 40rpx;
}

// 动画定义
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.6;
  }
}

// 确保TextShow组件的弹窗在最顶层
:deep(.apple-modal-popup) {
  z-index: 100000 !important;
}

// 页面层级设置
.help-page {
  position: relative;
  z-index: 1;
  isolation: isolate;
}
</style>
