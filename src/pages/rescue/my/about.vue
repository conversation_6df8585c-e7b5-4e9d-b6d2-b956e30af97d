<script setup lang="ts">
import TextShow from '@/components/common/meta/TextShow.vue'

// 页面状态
const pageLoaded = ref(false)

// 关于我们的文本配置
const aboutTexts = [
  {
    key: 'app-intro',
    title: '应用介绍',
    icon: 'info-circle',
  },
  {
    key: 'company-info',
    title: '公司信息',
    icon: 'building',
  },
  {
    key: 'version-info',
    title: '版本信息',
    icon: 'tag',
  },
  {
    key: 'contact-us',
    title: '联系我们',
    icon: 'phone',
  },
  {
    key: 'privacy-policy',
    title: '隐私政策',
    icon: 'shield-check',
  },
  {
    key: 'terms-service',
    title: '服务条款',
    icon: 'file-text',
  },
]

// 页面加载动画
function startPageAnimation() {
  pageLoaded.value = true
}

onMounted(() => {
  startPageAnimation()

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: '关于我们',
  })
})
</script>

<template>
  <view class="about-page" :class="{ loaded: pageLoaded }">
    <!-- 应用Logo和标题 -->
    <view class="app-header">
      <view class="app-logo">
        <image src="/static/images/logo.png" class="logo-image" mode="aspectFit" />
      </view>
      <view class="app-info">
        <view class="app-name">
          应急管理系统
        </view>
        <view class="app-version">
          v1.0.0
        </view>
        <view class="app-description">
          专业的应急救援管理平台
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y enhanced>
      <view class="content-sections">
        <view
          v-for="(textConfig, index) in aboutTexts"
          :key="textConfig.key"
          class="content-item"
          :style="{ 'animation-delay': `${index * 0.1}s` }"
        >
          <view class="item-header">
            <wd-icon :name="textConfig.icon" size="32rpx" color="#007AFF" />
            <text class="item-title">
              {{ textConfig.title }}
            </text>
          </view>

          <!-- 使用TextShow组件展示内容 -->
          <TextShow
            :text-key="textConfig.key"
            custom-class="about-text-show"
          />
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-info">
        <view class="copyright">
          <text>© 2024 应急管理系统</text>
        </view>
        <view class="powered-by">
          <text>Powered by Glowxq</text>
        </view>
      </view>

      <!-- 安全区域 -->
      <view class="safe-area-bottom" />
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统颜色
$system-blue: #007aff;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray4: #d1d1d6;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;

// 文本颜色
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$label-tertiary: rgba(60, 60, 67, 0.3);

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f2f2f7;

.about-page {
  min-height: 100vh;
  background: $background-secondary;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);

  &.loaded {
    opacity: 1;
    transform: translateY(0);
  }
}

// 应用头部
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80rpx 40rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: breathe 15s infinite ease-in-out;
  }

  .app-logo {
    width: 120rpx;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32rpx;
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);

    .logo-image {
      width: 80rpx;
      height: 80rpx;
    }
  }

  .app-info {
    text-align: center;
    position: relative;
    z-index: 2;

    .app-name {
      font-size: 40rpx;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 12rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }

    .app-version {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 16rpx;
      font-weight: 500;
    }

    .app-description {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 400;
      line-height: 1.4;
    }
  }
}

// 内容区域
.content-area {
  flex: 1;
  height: calc(100vh - 300rpx);
}

.content-sections {
  padding: 40rpx;
}

.content-item {
  margin-bottom: 40rpx;
  .item-header {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;
    padding: 0 8rpx;

    .item-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $label-primary;
      letter-spacing: -0.5rpx;
    }
  }
}

// TextShow组件自定义样式
:deep(.about-text-show) {
  .announcement-container,
  .information-container,
  .default-container {
    background: $background-primary;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
  }

  .agreement-container {
    background: $background-primary;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
  }

  .cell-container {
    .cell-item {
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    }
  }
}

// 底部信息
.footer-info {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;

  .copyright {
    font-size: 24rpx;
    color: $label-tertiary;
    margin-bottom: 12rpx;
  }

  .powered-by {
    font-size: 22rpx;
    color: $label-tertiary;
  }
}

// 安全区域
.safe-area-bottom {
  height: 40rpx;
}

// 动画定义
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.6;
  }
}

// 确保TextShow组件的弹窗在最顶层
:deep(.apple-modal-popup) {
  z-index: 100000 !important;
}

// 页面层级设置
.about-page {
  position: relative;
  z-index: 1;
  isolation: isolate;
}
</style>
