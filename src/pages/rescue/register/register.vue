<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    backgroundColor: '#f2f2f7',
  },
}
</route>

<script setup lang="ts">
import type { UserInfoRegisterDTO } from '@/api/interface/system/admin/login'
import { userRegisterApi } from '@/api/modules/system/admin/register'
// 导入枚举
import { Sex } from '@/enums/common/Sex'
import { BloodType } from '@/enums/rescue/BloodType'
import { PoliticsStatus } from '@/enums/rescue/PoliticsStatus'

// 响应式数据
const loading = ref(false)

// 生日选择范围
const minBirthday = new Date(1950, 0, 1).getTime() // 1950年1月1日
const maxBirthday = new Date().getTime() // 今天

// 注册表单
const registerForm = reactive<UserInfoRegisterDTO>({
  name: '',
  phone: '',
  nickName: '',
  avatar: '',
  deptId: undefined,
  identityCard: '',
  identityStartDate: '',
  identityEndDate: '',
  passportNumber: '',
  insuranceStatus: false,
  politicsStatus: '',
  bloodType: '',
  sex: '',
  birthday: '',
  remark: '',
  signatureImage: '',
  identityImage: '',
  informationImage: '',
  emergencyContact: '',
  emergencyContactPhone: '',
  medicalHistory: '',
  allergiesHistory: '',
  approveTime: '',
})

// 表单验证状态
const validation = reactive({
  name: { valid: true, message: '' },
  phone: { valid: true, message: '' },
  identityCard: { valid: true, message: '' },
  emergencyContact: { valid: true, message: '' },
  emergencyContactPhone: { valid: true, message: '' },
})

// 表单验证
function validateForm() {
  let isValid = true

  // 验证姓名
  if (!registerForm.name.trim()) {
    validation.name = { valid: false, message: '请输入姓名' }
    isValid = false
  } else if (registerForm.name.length < 2) {
    validation.name = { valid: false, message: '姓名至少2个字符' }
    isValid = false
  } else {
    validation.name = { valid: true, message: '' }
  }

  // 验证手机号
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!registerForm.phone.trim()) {
    validation.phone = { valid: false, message: '请输入手机号' }
    isValid = false
  } else if (!phoneRegex.test(registerForm.phone)) {
    validation.phone = { valid: false, message: '请输入正确的手机号格式' }
    isValid = false
  } else {
    validation.phone = { valid: true, message: '' }
  }

  // 验证身份证
  if (registerForm.identityCard) {
    const idCardRegex = /^(?:\d{15}|\d{18}|\d{17}X)$/i
    if (!idCardRegex.test(registerForm.identityCard)) {
      validation.identityCard = { valid: false, message: '请输入正确的身份证号格式' }
      isValid = false
    } else {
      validation.identityCard = { valid: true, message: '' }
    }
  } else {
    validation.identityCard = { valid: true, message: '' }
  }

  // 验证紧急联系人
  if (registerForm.emergencyContact && registerForm.emergencyContact.length < 2) {
    validation.emergencyContact = { valid: false, message: '紧急联系人姓名至少2个字符' }
    isValid = false
  } else {
    validation.emergencyContact = { valid: true, message: '' }
  }

  // 验证紧急联系人电话
  if (registerForm.emergencyContactPhone && !phoneRegex.test(registerForm.emergencyContactPhone)) {
    validation.emergencyContactPhone = { valid: false, message: '请输入正确的紧急联系人电话格式' }
    isValid = false
  } else {
    validation.emergencyContactPhone = { valid: true, message: '' }
  }

  return isValid
}

// 处理注册
async function handleRegister() {
  if (!validateForm()) {
    uni.vibrateShort()
    return
  }

  loading.value = true

  try {
    // 添加触觉反馈
    uni.vibrateShort()

    // 格式化时间字段
    const formData = { ...registerForm }

    // 格式化生日为 yyyy-MM-dd 字符串（Java LocalDate格式）
    if (formData.birthday) {
      // 确保生日是字符串格式，而不是时间戳
      if (typeof formData.birthday === 'number') {
        formData.birthday = formatDate(new Date(formData.birthday))
      } else {
        // 如果已经是字符串格式，确保格式正确
        formData.birthday = formatDate(formData.birthday)
      }
    }

    // 格式化身份证有效期
    if (formData.identityStartDate) {
      formData.identityStartDate = formatDate(formData.identityStartDate)
    }
    if (formData.identityEndDate) {
      formData.identityEndDate = formatDate(formData.identityEndDate)
    }

    // 格式化审批时间（如果需要的话）
    if (formData.approveTime) {
      formData.approveTime = formatDateTime(formData.approveTime)
    }

    await userRegisterApi(formData)

    // 成功反馈
    uni.vibrateShort()
    uni.showToast({
      title: '注册成功',
      icon: 'success',
      duration: 2000,
    })

    // 延迟跳转到登录页面
    setTimeout(() => {
      uni.navigateBack({
        delta: 1,
      })
    }, 1500)
  } catch (error: any) {
    console.error('注册失败:', error)
    // 错误反馈
    uni.vibrateShort()
    uni.showToast({
      title: error.msg || '注册失败，请检查信息并重试',
      icon: 'none',
      duration: 3000,
    })
  } finally {
    loading.value = false
  }
}

// 重置表单
function resetForm() {
  Object.assign(registerForm, {
    name: '',
    phone: '',
    nickName: '',
    avatar: '',
    deptId: undefined,
    identityCard: '',
    identityStartDate: '',
    identityEndDate: '',
    passportNumber: '',
    insuranceStatus: false,
    politicsStatus: '',
    bloodType: '',
    sex: '',
    birthday: '',
    remark: '',
    signatureImage: '',
    identityImage: '',
    informationImage: '',
    emergencyContact: '',
    emergencyContactPhone: '',
    medicalHistory: '',
    allergiesHistory: '',
    approveTime: '',
  })

  // 重置验证状态
  Object.keys(validation).forEach((key) => {
    validation[key as keyof typeof validation] = { valid: true, message: '' }
  })

  uni.vibrateShort()
}

// 返回登录页
function goToLogin() {
  uni.vibrateShort()

  // 添加页面切换动画
  uni.navigateTo({
    url: '/pages/rescue/login/login',
    animationType: 'slide-in-left',
    animationDuration: 300,
  })
}

// 输入框聚焦处理
function handleInputFocus(field: string) {
  if (validation[field as keyof typeof validation]) {
    validation[field as keyof typeof validation] = { valid: true, message: '' }
  }
}

// 清空生日
function clearBirthday() {
  registerForm.birthday = ''
  uni.vibrateShort()
}

// 清空身份证有效期开始
function clearIdentityStartDate() {
  registerForm.identityStartDate = ''
  uni.vibrateShort()
}

// 清空身份证有效期截止
function clearIdentityEndDate() {
  registerForm.identityEndDate = ''
  uni.vibrateShort()
}

// 清空入队时间
function clearApproveTime() {
  registerForm.approveTime = ''
  uni.vibrateShort()
}

// 生成测试数据
function generateTestData() {
  const testNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const testNickNames = ['小张', '小李', '小王', '小赵', '小钱', '小孙', '小周', '小吴']
  const testPhones = ['13800138001', '13800138002', '13800138003', '13800138004', '13800138005']
  const testIdentityCards = ['110101199001011234', '110101199002021234', '110101199003031234']
  const testEmergencyContacts = ['张父', '李母', '王兄', '赵姐', '钱叔', '孙姨']
  const testEmergencyPhones = ['13900139001', '13900139002', '13900139003', '13900139004']

  // 随机选择测试数据
  const randomName = testNames[Math.floor(Math.random() * testNames.length)]
  const randomNickName = testNickNames[Math.floor(Math.random() * testNickNames.length)]
  const randomPhone = testPhones[Math.floor(Math.random() * testPhones.length)]
  const randomIdentityCard = testIdentityCards[Math.floor(Math.random() * testIdentityCards.length)]
  const randomEmergencyContact =
    testEmergencyContacts[Math.floor(Math.random() * testEmergencyContacts.length)]
  const randomEmergencyPhone =
    testEmergencyPhones[Math.floor(Math.random() * testEmergencyPhones.length)]

  // 生成随机生日（1950-2000年之间）
  const startYear = 1950
  const endYear = 2000
  const randomYear = startYear + Math.floor(Math.random() * (endYear - startYear + 1))
  const randomMonth = Math.floor(Math.random() * 12) + 1
  const randomDay = Math.floor(Math.random() * 28) + 1
  const randomBirthday = `${randomYear}-${String(randomMonth).padStart(2, '0')}-${String(randomDay).padStart(2, '0')}`

  // 生成随机身份证有效期（2020-2030年之间）
  const startIdentityYear = 2020
  const endIdentityYear = 2030
  const randomIdentityStartDate = `${startIdentityYear}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`
  const randomIdentityEndDate = `${endIdentityYear}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`

  // 生成随机入队时间（2023-2024年之间）
  const startApproveYear = 2023
  const endApproveYear = 2024
  const randomApproveTime = `${startApproveYear + Math.floor(Math.random() * (endApproveYear - startApproveYear + 1))}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`

  // 随机选择枚举值
  const politicsStatusOptions = Object.values(PoliticsStatus)
  const bloodTypeOptions = Object.values(BloodType)
  const sexOptions = Object.values(Sex)

  const randomPoliticsStatus =
    politicsStatusOptions[Math.floor(Math.random() * politicsStatusOptions.length)]
  const randomBloodType = bloodTypeOptions[Math.floor(Math.random() * bloodTypeOptions.length)]
  const randomSex = sexOptions[Math.floor(Math.random() * sexOptions.length)]

  // 填充表单数据
  Object.assign(registerForm, {
    name: randomName,
    nickName: randomNickName,
    phone: randomPhone,
    deptId: 1, // 默认部门ID
    identityCard: randomIdentityCard,
    passportNumber: 'E12345678',
    insuranceStatus: Math.random() > 0.5,
    politicsStatus: randomPoliticsStatus.code, // 使用枚举code
    bloodType: randomBloodType.code, // 使用枚举code
    sex: randomSex.code, // 使用枚举code
    birthday: randomBirthday,
    identityStartDate: randomIdentityStartDate,
    identityEndDate: randomIdentityEndDate,
    approveTime: randomApproveTime,
    remark: '这是一条测试数据',
    emergencyContact: randomEmergencyContact,
    emergencyContactPhone: randomEmergencyPhone,
    medicalHistory: '无重大疾病史',
    allergiesHistory: '无过敏史',
  })

  // 重置验证状态
  Object.keys(validation).forEach((key) => {
    validation[key as keyof typeof validation] = { valid: true, message: '' }
  })

  uni.vibrateShort()
  uni.showToast({
    title: '测试数据已生成',
    icon: 'success',
    duration: 1500,
  })
}

// 格式化日期为 yyyy-MM-dd
function formatDate(date: string | Date | number): string {
  if (!date) return ''

  let d: Date
  if (typeof date === 'number') {
    d = new Date(date)
  } else if (typeof date === 'string') {
    d = new Date(date)
  } else {
    d = date
  }

  if (Number.isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 格式化日期时间为 yyyy-MM-dd hh:mm:ss
function formatDateTime(date: string | Date): string {
  if (!date) return ''

  const d = new Date(date)
  if (Number.isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 组件挂载
onMounted(() => {
  // 页面初始化逻辑
})
</script>

<template>
  <view class="register-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @tap="goToLogin">
          <wd-icon name="arrow-left" size="40rpx" color="#007AFF" />
          <view class="navbar-back-text">返回</view>
        </view>
        <view class="navbar-title">用户注册</view>
        <view class="navbar-right">
          <view class="test-btn" @tap="generateTestData">
            <wd-icon name="setting" size="36rpx" color="#007AFF" />
            <view class="test-text">测试</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主内容容器 -->
    <view class="main-container">
      <!-- 头部区域 -->
      <view class="header-area">
        <view class="logo-section">
          <view class="logo-ring">
            <image src="/static/logo.svg" class="logo-image" mode="aspectFit" />
          </view>
          <view class="title-group">
            <view class="app-name">用户注册</view>
            <view class="app-subtitle">请填写您的个人信息</view>
          </view>
        </view>
      </view>

      <!-- 表单区域 -->
      <scroll-view
        class="form-container"
        scroll-y
        enhanced
        :show-scrollbar="false"

      >
        <view class="form-inner">
          <!-- 基本信息 -->
          <view class="form-card">
            <view class="card-header">
              <view class="header-icon">
                <wd-icon name="user" size="44rpx" color="#007AFF" />
              </view>
              <view class="header-title">基本信息</view>
            </view>

            <!-- 姓名 -->
            <view class="input-group" :class="{ error: !validation.name.valid }">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="user" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.name"
                  placeholder="请输入真实姓名"
                  type="text"
                  class="input-field"
                  @focus="handleInputFocus('name')"
                  @blur="validateForm"
                />
              </view>
              <view v-if="!validation.name.valid" class="error-message">
                {{ validation.name.message }}
              </view>
            </view>

            <!-- 昵称 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="edit" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.nickName"
                  placeholder="请输入昵称（可选）"
                  type="text"
                  class="input-field"
                />
              </view>
            </view>

            <!-- 手机号 -->
            <view class="input-group" :class="{ error: !validation.phone.valid }">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="phone" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.phone"
                  placeholder="请输入手机号"
                  type="number"
                  class="input-field"
                  @focus="handleInputFocus('phone')"
                  @blur="validateForm"
                />
              </view>
              <view v-if="!validation.phone.valid" class="error-message">
                {{ validation.phone.message }}
              </view>
            </view>

            <!-- 部门选择 -->
            <view class="input-group">
              <view class="">
                <view class="">
                  <DeptSelect
                    v-model="registerForm.deptId"
                    placeholder="请选择所属部门"
                    class="select-component"
                  />
                </view>
              </view>
            </view>

            <!-- 性别选择 -->
            <view class="input-group">
              <view class="input-label">性别</view>
              <view class="enum-container">
                <EnumSelect
                  v-model="registerForm.sex"
                  :enum-data="Sex"
                  type="tab"
                  placeholder="请选择性别"
                />
              </view>
            </view>
          </view>

          <!-- 身份信息 -->
          <view class="form-card">
            <view class="card-header">
              <view class="header-icon">
                <wd-icon name="id-card" size="44rpx" color="#007AFF" />
              </view>
              <view class="header-title">身份信息</view>
            </view>

            <!-- 生日 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="star" size="44rpx" color="#8E8E93" />
                </view>
                <view class="select-container">
                  <wd-datetime-picker
                    v-model="registerForm.birthday"
                    type="date"
                    placeholder="请选择生日"
                    format="yyyy-MM-dd"
                    :min-date="minBirthday"
                    :max-date="maxBirthday"
                    custom-class="picker-component"
                  />
                </view>
                <view v-if="registerForm.birthday" class="input-suffix" @tap="clearBirthday">
                  <wd-icon name="close" size="36rpx" color="#8E8E93" />
                </view>
              </view>
            </view>

            <!-- 身份证有效期开始 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="calendar" size="44rpx" color="#8E8E93" />
                </view>
                <view class="select-container">
                  <wd-datetime-picker
                    v-model="registerForm.identityStartDate"
                    type="date"
                    placeholder="请选择身份证有效期开始日期"
                    format="yyyy-MM-dd"
                    custom-class="picker-component"
                  />
                </view>
                <view
                  v-if="registerForm.identityStartDate"
                  class="input-suffix"
                  @tap="clearIdentityStartDate"
                >
                  <wd-icon name="close" size="36rpx" color="#8E8E93" />
                </view>
              </view>
            </view>

            <!-- 身份证有效期截止 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="calendar" size="44rpx" color="#8E8E93" />
                </view>
                <view class="select-container">
                  <wd-datetime-picker
                    v-model="registerForm.identityEndDate"
                    type="date"
                    placeholder="请选择身份证有效期截止日期"
                    format="yyyy-MM-dd"
                    custom-class="picker-component"
                  />
                </view>
                <view
                  v-if="registerForm.identityEndDate"
                  class="input-suffix"
                  @tap="clearIdentityEndDate"
                >
                  <wd-icon name="close" size="36rpx" color="#8E8E93" />
                </view>
              </view>
            </view>

            <!-- 入队时间 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="clock" size="44rpx" color="#8E8E93" />
                </view>
                <view class="select-container">
                  <wd-datetime-picker
                    v-model="registerForm.approveTime"
                    type="datetime"
                    placeholder="请选择入队时间"
                    format="yyyy-MM-dd HH:mm:ss"
                    custom-class="picker-component"
                  />
                </view>
                <view v-if="registerForm.approveTime" class="input-suffix" @tap="clearApproveTime">
                  <wd-icon name="close" size="36rpx" color="#8E8E93" />
                </view>
              </view>
            </view>

            <!-- 血型 -->
            <view class="input-group">
              <view class="input-wrapper">
                <EnumSelect
                  v-model="registerForm.bloodType"
                  :enum-data="BloodType"
                  placeholder="请选择血型"
                />
              </view>
            </view>

            <!-- 政治面貌 -->
            <view class="input-group">
              <view class="input-wrapper">
                <EnumSelect
                  v-model="registerForm.politicsStatus"
                  :enum-data="PoliticsStatus"
                  type="mobile"
                  placeholder="请选择政治面貌"
                />
              </view>
            </view>

            <!-- 身份证号 -->
            <view class="input-group" :class="{ error: !validation.identityCard.valid }">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="file" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.identityCard"
                  placeholder="请输入身份证号（可选）"
                  type="text"
                  class="input-field"
                  @focus="handleInputFocus('identityCard')"
                  @blur="validateForm"
                />
              </view>
              <view v-if="!validation.identityCard.valid" class="error-message">
                {{ validation.identityCard.message }}
              </view>
            </view>

            <!-- 护照号 -->
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="file" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.passportNumber"
                  placeholder="请输入护照号（可选）"
                  type="text"
                  class="input-field"
                />
              </view>
            </view>
          </view>

          <!-- 紧急联系信息 -->
          <view class="form-card">
            <view class="card-header">
              <view class="header-icon">
                <wd-icon name="phone" size="44rpx" color="#007AFF" />
              </view>
              <view class="header-title">紧急联系信息</view>
            </view>

            <!-- 紧急联系人 -->
            <view class="input-group" :class="{ error: !validation.emergencyContact.valid }">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="user" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.emergencyContact"
                  placeholder="请输入紧急联系人姓名"
                  type="text"
                  class="input-field"
                  @focus="handleInputFocus('emergencyContact')"
                  @blur="validateForm"
                />
              </view>
              <view v-if="!validation.emergencyContact.valid" class="error-message">
                {{ validation.emergencyContact.message }}
              </view>
            </view>

            <!-- 紧急联系人电话 -->
            <view class="input-group" :class="{ error: !validation.emergencyContactPhone.valid }">
              <view class="input-wrapper">
                <view class="input-prefix">
                  <wd-icon name="phone" size="44rpx" color="#8E8E93" />
                </view>
                <input
                  v-model="registerForm.emergencyContactPhone"
                  placeholder="请输入紧急联系人电话"
                  type="number"
                  class="input-field"
                  @focus="handleInputFocus('emergencyContactPhone')"
                  @blur="validateForm"
                />
              </view>
              <view v-if="!validation.emergencyContactPhone.valid" class="error-message">
                {{ validation.emergencyContactPhone.message }}
              </view>
            </view>
          </view>

          <!-- 健康与其他信息 -->
          <view class="form-card">
            <view class="card-header">
              <view class="header-icon">
                <wd-icon name="heart" size="44rpx" color="#007AFF" />
              </view>
              <view class="header-title">健康与其他信息</view>
            </view>

            <!-- 保险状态 -->
            <view class="input-group">
              <view class="switch-row">
                <view class="switch-label">是否有保险</view>
                <wd-switch v-model="registerForm.insuranceStatus" custom-class="switch-component" />
              </view>
            </view>

            <!-- 医疗史 -->
            <view class="input-group">
              <view class="input-label">医疗史</view>
              <wd-textarea
                v-model="registerForm.medicalHistory"
                placeholder="请描述个人医疗史（可选）"
                :maxlength="500"
                show-word-limit
                auto-height
                custom-class="textarea-component"
              />
            </view>

            <!-- 过敏史 -->
            <view class="input-group">
              <view class="input-label">过敏史</view>
              <wd-textarea
                v-model="registerForm.allergiesHistory"
                placeholder="请描述过敏史（可选）"
                :maxlength="500"
                show-word-limit
                auto-height
                custom-class="textarea-component"
              />
            </view>

            <!-- 备注 -->
            <view class="input-group">
              <view class="input-label">备注</view>
              <wd-textarea
                v-model="registerForm.remark"
                placeholder="请输入备注信息（可选）"
                :maxlength="500"
                show-word-limit
                auto-height
                custom-class="textarea-component"
              />
            </view>
          </view>

          <!-- 文件上传区域 -->
          <view class="form-card">
            <view class="card-header">
              <view class="header-icon">
                <wd-icon name="camera" size="44rpx" color="#007AFF" />
              </view>
              <view class="header-title">文件上传</view>
            </view>

            <!-- 头像上传 -->
            <view class="input-group">
              <view class="input-label">头像</view>
              <UploadImage
                v-model:image-url="registerForm.avatar"
                height="120px"
                width="120px"
                :limit="1"
                title="上传头像"
                dir="avatar"
                class="upload-component"
              />
            </view>

            <!-- 身份证照片 -->
            <view class="input-group">
              <view class="input-label">身份证照片</view>
              <UploadImage
                v-model:image-url="registerForm.identityImage"
                height="100px"
                width="160px"
                :limit="1"
                title="上传身份证"
                dir="identity"
                class="upload-component"
              />
            </view>

            <!-- 电子签名 -->
            <view class="input-group">
              <view class="input-label">电子签名</view>
              <DrawSign
                v-model="registerForm.signatureImage"
                height="120px"
                width="100%"
                title="电子签名"
                placeholder="点击进行电子签名"
                dir="signature"
                class="sign-component"
              />
            </view>

            <!-- 相关资料 -->
            <view class="input-group">
              <view class="input-label">相关资料</view>
              <UploadImage
                v-model:image-url="registerForm.informationImage"
                height="100px"
                width="100px"
                :limit="5"
                :multiple="true"
                title="上传资料"
                dir="information"
                class="upload-component"
              />
            </view>
          </view>

          <!-- 底部空白区域 -->
          <view class="bottom-space" />
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-buttons">
        <button class="reset-btn" @tap="resetForm">
          <wd-icon name="refresh" size="40rpx" color="#8E8E93" />
          <view class="btn-text">重置</view>
        </button>

        <button class="register-btn" :class="{ loading }" :disabled="loading" @tap="handleRegister">
          <view v-if="!loading" class="btn-content">
            <wd-icon name="check" size="40rpx" color="#FFFFFF" />
            <view class="btn-text">注册</view>
          </view>
          <view v-else class="btn-content">
            <wd-icon name="loading" size="40rpx" color="#FFFFFF" class="loading-icon" />
            <view class="btn-text">注册中...</view>
          </view>
        </button>
      </view>

      <view class="back-link" @tap="goToLogin">
        <view class="link-text">已有账号？返回登录</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 全局盒模型设置
.register-page {
  box-sizing: border-box;
}

// Apple设计系统变量
$primary-color: #007aff;
$secondary-color: #5ac8fa;
$success-color: #34c759;
$error-color: #ff3b30;
$text-primary: #1d1d1f;
$text-secondary: #8e8e93;
$text-tertiary: #c7c7cc;
$background-primary: #f2f2f7;
$background-secondary: #ffffff;
$border-color: rgba(60, 60, 67, 0.18);
$shadow-light: rgba(0, 0, 0, 0.05);
$shadow-medium: rgba(0, 0, 0, 0.1);

// 页面容器 - 限制在视窗高度内
.register-page {
  height: 100vh; // 使用完整视窗高度
  background: $background-primary;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止页面滚动
  // 确保页面容器高度正确，不产生滚动条
  box-sizing: border-box;
  position: relative;
  // 确保页面不会产生滚动条
  min-height: 100vh;
  max-height: 100vh;


}

// 自定义导航栏
.custom-navbar {
  flex-shrink: 0; // 防止被压缩
  background: $background-primary;
  padding-top: env(safe-area-inset-top); // 安全区域顶部
  border-bottom: 2rpx solid $border-color;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 1000;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx; // 标准导航栏高度
    padding: 0 32rpx;

    .navbar-left {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx;
      border-radius: 8rpx;


      &:active {
        background: rgba(0, 122, 255, 0.15);
      }

      .navbar-back-text {
        font-size: 32rpx;
        font-weight: 500;
        color: $primary-color;
      }
    }

    .navbar-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
      letter-spacing: -0.5rpx;
    }

    .navbar-right {
      width: 80rpx; // 保持对称

      .test-btn {
        display: flex;
        align-items: center;
        gap: 4rpx;
        padding: 8rpx 12rpx;
        border-radius: 8rpx;
        background: rgba(0, 122, 255, 0.1);

        &:active {
          background: rgba(0, 122, 255, 0.25);
        }

        .test-text {
          font-size: 24rpx;
          font-weight: 500;
          color: $primary-color;
        }
      }
    }
  }
}

// 主内容容器 - 可滚动区域
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止容器本身滚动
  opacity: 1;
  // 确保容器高度正确，不产生滚动条
  box-sizing: border-box;
  position: relative;
}

// 头部区域 - 固定高度
.header-area {
  flex-shrink: 0; // 防止被压缩
  text-align: center;
  padding: 40rpx 48rpx 20rpx;
  opacity: 1;
  // 确保头部区域正确显示，不产生滚动条
  box-sizing: border-box;
  position: relative;
  background: $background-primary;

  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;

    .logo-ring {
      width: 100rpx;
      height: 100rpx;
      background: linear-gradient(135deg, $primary-color, $secondary-color);
      border-radius: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24rpx;
      box-shadow: 0 6rpx 24rpx rgba(0, 122, 255, 0.3);

      .logo-image {
        width: 60rpx;
        height: 60rpx;
        filter: brightness(0) invert(1);
      }
    }

    .title-group {
      display: flex;
      flex-direction: column;
      align-items: center;

      .app-name {
        font-size: 48rpx;
        font-weight: 700;
        color: $text-primary;
        letter-spacing: -1.5rpx;
        margin-bottom: 6rpx;
      }

      .app-subtitle {
        font-size: 28rpx;
        font-weight: 500;
        color: $text-secondary;
        letter-spacing: 1.5rpx;
      }
    }
  }
}

// 表单容器 - 可滚动区域
.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止容器本身滚动
  opacity: 1;
  // 确保容器高度正确，不产生滚动条
  box-sizing: border-box;
  position: relative;

  .form-inner {
    flex: 1;
    overflow-y: auto; // 表单内容可滚动
    padding: 0 48rpx 20rpx;
    // 隐藏滚动条但保持滚动功能
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge
    // 确保滚动区域高度正确，不产生滚动条
    box-sizing: border-box;
    position: relative;
    &::-webkit-scrollbar {
      display: none; // Chrome/Safari
    }

    .form-card {
      background: $background-secondary;
      border-radius: 24rpx;
      padding: 40rpx 32rpx;
      box-shadow: 0 4rpx 20rpx $shadow-light;
      margin-bottom: 24rpx;

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;
        padding-bottom: 12rpx;
        border-bottom: 2rpx solid rgba(0, 122, 255, 0.1);

        .header-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 44rpx;
          height: 44rpx;
          background: rgba(0, 122, 255, 0.1);
          border-radius: 10rpx;
          margin-right: 12rpx;
        }

        .header-title {
          font-size: 30rpx;
          font-weight: 600;
          color: $text-primary;
          letter-spacing: -0.3rpx;
        }
      }

      .input-group {
        margin-bottom: 28rpx;

        &:last-child {
          margin-bottom: 0;
        }

        &.error .input-wrapper {
          border-color: $error-color;
          background: rgba(255, 59, 48, 0.05);
        }

        .input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          background: $background-secondary;
          border: 2rpx solid $border-color;
          border-radius: 16rpx;
          height: 100rpx;

          &:focus-within {
            border-color: $primary-color;
            box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
          }

          .input-prefix {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 72rpx;
            height: 100%;
          }

          .input-field {
            flex: 1;
            height: 100%;
            font-size: 32rpx;
            font-weight: 500;
            color: $text-primary;
            background: transparent;
            border: none;
            outline: none;

            &::placeholder {
              color: $text-tertiary;
              font-weight: 400;
            }
          }

          .select-container {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;

            :deep(.picker-component) {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              font-size: 32rpx;
              font-weight: 500;
              color: $text-primary;
              background: transparent;
              border: none;
              outline: none;

              &::placeholder {
                color: $text-tertiary;
                font-weight: 400;
              }
            }
          }

          .input-suffix {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 72rpx;
            height: 100%;
            padding: 12rpx;
            border-radius: 8rpx;

            &:active {
              background: rgba(142, 142, 147, 0.15);
            }
          }
        }

        .input-label {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: $text-primary;
          margin-bottom: 12rpx;
          padding-left: 8rpx;
        }

        .enum-container {
          margin-top: 6rpx;
        }

        .switch-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20rpx;
          background: $background-secondary;
          border: 2rpx solid $border-color;
          border-radius: 16rpx;

          .switch-label {
            font-size: 32rpx;
            font-weight: 500;
            color: $text-primary;
          }
        }

        .error-message {
          display: block;
          font-size: 24rpx;
          color: $error-color;
          margin-top: 12rpx;
          margin-left: 20rpx;
        }

        :deep(.textarea-component) {
          margin-top: 6rpx;
          border: 2rpx solid $border-color;
          border-radius: 16rpx;
          background: $background-secondary;
          padding: 12rpx;
          font-size: 28rpx;
        }

        .upload-component,
        .sign-component {
          margin-top: 6rpx;
        }
      }
    }

    // 底部留白，确保最后的内容不被底部操作栏遮挡
    .bottom-space {
      height: calc(120rpx + env(safe-area-inset-bottom));
      min-height: 120rpx;
      flex-shrink: 0; // 防止被压缩
    }
  }
}

// 底部操作栏 - 固定在视窗底部
.bottom-actions {
  flex-shrink: 0; // 防止被压缩
  background: $background-secondary;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid $border-color;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  // 确保底部操作栏正确显示，不产生滚动条
  box-sizing: border-box;
  position: relative;
  z-index: 100;

  .action-buttons {
    display: flex;
    gap: 16rpx;
    margin-bottom: 16rpx;

    .reset-btn,
    .register-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100rpx;
      border-radius: 16rpx;
      font-size: 34rpx;
      font-weight: 600;
      border: none;
      position: relative;
      overflow: hidden;

      .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-text {
          margin-left: 12rpx;
          font-size: 34rpx;
          font-weight: 600;
        }
      }

      &:active:not(.disabled) {
        opacity: 0.8;
      }
    }

    .reset-btn {
      flex: 1;
      background: rgba(142, 142, 147, 0.1);
      color: $text-secondary;

      &:active {
        background: rgba(142, 142, 147, 0.15);
      }
    }

    .register-btn {
      flex: 2;
      background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
      color: #ffffff;
      box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.3);

      &.loading {
        background: linear-gradient(135deg, #5ac8fa, #34c759);
      }

      &.disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      &:active:not(.disabled) {
        opacity: 0.8;
      }

      .loading-icon {
        animation: spin 1s linear infinite;
      }
    }
  }

  .back-link {
    text-align: center;

    .link-text {
      font-size: 28rpx;
      color: $text-secondary;
      padding: 12rpx;


      &:active {
        opacity: 0.7;
      }
    }
  }
}

// 保留必要的动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
