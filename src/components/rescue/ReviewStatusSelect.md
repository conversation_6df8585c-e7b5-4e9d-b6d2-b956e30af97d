# ReviewStatusSelect 审批状态选择器

## 概述

ReviewStatusSelect 是一个专为移动端设计的审批状态选择组件，采用 Apple 设计标准，提供流畅的触摸交互体验。组件支持审批通过、拒绝等状态选择，适用于各种审批场景。

## 特性

- 🎨 **Apple 设计标准**：遵循 Apple Human Interface Guidelines
- 📱 **移动端优化**：专为触摸交互设计，支持触觉反馈
- 🌙 **深色模式**：自动适配系统主题
- ✨ **流畅动画**：优雅的交互动画效果
- 🎛️ **灵活配置**：支持多种显示模式和尺寸
- ♿ **可访问性**：支持屏幕阅读器和键盘导航

## 设计亮点

### 视觉设计

- 卡片式布局，每个选项独立显示
- 毛玻璃背景效果，增强视觉层次
- 状态色彩区分：绿色（通过）、红色（拒绝）
- 选中状态有明显的视觉反馈
- 支持悬停和点击动画

### 交互体验

- 触摸反馈：点击时提供触觉反馈
- 缩放动画：点击时卡片轻微缩放
- 选中指示器：蓝色圆形选中标记
- 状态切换：流畅的状态转换动画

### 信息架构

- 图标 + 文本的清晰布局
- 状态名称和描述分层展示
- 错误信息独立显示区域
- 必填标记和验证提示

## Props

| 属性名         | 类型                             | 默认值       | 说明                 |
| -------------- | -------------------------------- | ------------ | -------------------- |
| `modelValue`   | `string`                         | `''`         | 绑定值，审批状态代码 |
| `disabled`     | `boolean`                        | `false`      | 是否禁用             |
| `size`         | `'small' \| 'medium' \| 'large'` | `'medium'`   | 组件尺寸             |
| `showTitle`    | `boolean`                        | `false`      | 是否显示标题         |
| `title`        | `string`                         | `'审批状态'` | 标题文本             |
| `required`     | `boolean`                        | `false`      | 是否必填             |
| `errorMessage` | `string`                         | `''`         | 错误信息             |
| `showNone`     | `boolean`                        | `false`      | 是否显示等待审批选项 |

## Events

| 事件名              | 参数                   | 说明               |
| ------------------- | ---------------------- | ------------------ |
| `update:modelValue` | `value: string`        | 值变化时触发       |
| `change`            | `value: string`        | 选择变化时触发     |
| `select`            | `option: ReviewStatus` | 选择具体选项时触发 |

## 使用示例

### 基础用法

```vue
<template>
  <ReviewStatusSelect
    v-model="selectedStatus"
    @change="handleStatusChange"
    @select="handleStatusSelect"
  />
</template>

<script setup>
import ReviewStatusSelect from '@/components/rescue/ReviewStatusSelect.vue'

const selectedStatus = ref('')

function handleStatusChange(value) {
  console.log('状态变化:', value)
}

function handleStatusSelect(option) {
  console.log('选择了:', option)
}
</script>
```

### 带标题和验证

```vue
<template>
  <ReviewStatusSelect
    v-model="selectedStatus"
    :show-title="true"
    title="审批结果"
    :required="true"
    :error-message="errorMessage"
    @change="handleStatusChange"
  />
</template>

<script setup>
const selectedStatus = ref('')
const errorMessage = ref('')

function handleStatusChange(value) {
  // 清除错误
  errorMessage.value = ''
}
</script>
```

### 不同尺寸

```vue
<template>
  <!-- 小尺寸 -->
  <ReviewStatusSelect v-model="selectedStatus" size="small" />

  <!-- 大尺寸 -->
  <ReviewStatusSelect v-model="selectedStatus" size="large" />
</template>
```

### 包含等待审批选项

```vue
<template>
  <ReviewStatusSelect
    v-model="selectedStatus"
    :show-none="true"
    :show-title="true"
    title="完整状态"
  />
</template>
```

## 审批状态枚举

组件使用 `ReviewStatus` 枚举，包含以下状态：

- **None**: 等待审批
- **Approved**: 审批通过
- **Rejected**: 审批拒绝

## 样式定制

组件使用 CSS 变量进行样式定制，支持以下变量：

```css
:root {
  --apple-blue: #007aff;
  --apple-green: #34c759;
  --apple-red: #ff3b30;
  --apple-background: #f2f2f7;
  --apple-surface: #ffffff;
  --apple-text: #1d1d1f;
  --apple-text-secondary: #86868b;
  --apple-radius: 12px;
  --apple-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
```

## 移动端适配

- 使用 `rpx` 单位确保在不同设备上的一致性
- 触摸反馈使用 ``
- 响应式设计适配不同屏幕尺寸
- 安全区域适配

## 深色模式

组件自动支持深色模式，通过 `@media (prefers-color-scheme: dark)` 检测系统主题并调整颜色。

## 注意事项

1. 组件依赖 `wot-design-uni` 组件库的 `wd-icon` 组件
2. 需要确保 `ReviewStatus` 枚举已正确导入
3. 触觉反馈功能需要设备支持
4. 建议在移动端环境中测试交互效果

## 更新日志

### v2.0.0 (重构版本)

- 完全重构为 uniapp + wot-design-ui 版本
- 采用 Apple 设计标准
- 优化移动端交互体验
- 添加触觉反馈
- 支持深色模式
- 增强可访问性
