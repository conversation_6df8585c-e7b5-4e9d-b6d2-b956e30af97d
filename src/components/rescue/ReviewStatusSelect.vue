<script setup lang="ts">
import { ReviewStatus } from '@/enums/rescue'

defineOptions({
  name: 'ReviewStatusSelect',
})

const modelValue = defineModel<string>({
  type: String,
  default: '',
})

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  size: 'medium',
  showTitle: false,
  title: '审批状态',
  required: false,
  errorMessage: '',
  showNone: false,
})

const emit = defineEmits<Emits>()

interface Props {
  /** 是否禁用 */
  disabled?: boolean
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否显示标题 */
  showTitle?: boolean
  /** 标题文本 */
  title?: string
  /** 是否必填 */
  required?: boolean
  /** 错误信息 */
  errorMessage?: string
  /** 是否显示等待审批选项 */
  showNone?: boolean
}

interface Emits {
  (e: 'select', option: (typeof ReviewStatus)[keyof typeof ReviewStatus]): void
}

// 审批选项
const reviewOptions = computed(() => {
  const options = []

  if (props.showNone) {
    options.push(ReviewStatus.None)
  }

  options.push(ReviewStatus.Approved, ReviewStatus.Rejected)

  return options
})

// 处理选择
function handleSelect(code: string) {
  if (props.disabled) {
    // 触觉反馈

    return
  }

  // 触觉反馈


  const selectedOption = reviewOptions.value.find((option) => option.code === code)
  modelValue.value = code
  if (selectedOption) {
    emit('select', selectedOption)
  }
}
</script>

<template>
  <view class="review-status-select" :class="`size-${size}`">
    <!-- 标题区域 -->
    <view v-if="showTitle" class="select-title">
      <view class="title-text">
        {{ title }}
      </view>
      <view v-if="required" class="required-mark">*</view>
    </view>

    <!-- 状态选项 -->
    <view class="status-options">
      <view
        v-for="option in reviewOptions"
        :key="option.code"
        class="status-option"
        :class="{
          selected: modelValue === option.code,
          approved: option.code === 'Approved',
          rejected: option.code === 'Rejected',
          none: option.code === 'None',
          disabled,
        }"
        @tap="handleSelect(option.code)"
      >
        <!-- 选项内容 -->
        <view class="option-content">
          <!-- 图标区域 -->
          <view class="option-icon-wrapper">
            <view class="option-icon">
              <wd-icon
                v-if="option.code === 'Approved'"
                name="check-circle"
                size="48rpx"
                color="#34C759"
              />
              <wd-icon
                v-else-if="option.code === 'Rejected'"
                name="close-circle"
                size="48rpx"
                color="#FF3B30"
              />
              <wd-icon
                v-else-if="option.code === 'None'"
                name="clock"
                size="48rpx"
                color="#8E8E93"
              />
            </view>
          </view>

          <!-- 文本区域 -->
          <view class="option-text">
            <view class="option-name">
              {{ option.name }}
            </view>
            <view class="option-desc">
              {{ option.description }}
            </view>
          </view>

          <!-- 选中指示器 -->
          <view v-if="modelValue === option.code" class="selected-indicator">
            <wd-icon name="check" size="32rpx" color="#FFFFFF" />
          </view>
        </view>

        <!-- 触摸反馈 -->
        <view class="touch-feedback" />
      </view>
    </view>

    <!-- 错误提示 -->
    <view v-if="errorMessage" class="error-message">
      <wd-icon name="warning" size="28rpx" color="#FF3B30" />
      <view class="error-text">
        {{ errorMessage }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// Apple 设计系统变量
:root {
  --apple-blue: #007aff;
  --apple-blue-light: #5ac8fa;
  --apple-blue-dark: #0056cc;
  --apple-green: #34c759;
  --apple-green-light: #30d158;
  --apple-green-dark: #248a3d;
  --apple-orange: #ff9500;
  --apple-orange-light: #ff9f0a;
  --apple-orange-dark: #c7750a;
  --apple-red: #ff3b30;
  --apple-red-light: #ff453a;
  --apple-red-dark: #d70015;
  --apple-purple: #af52de;
  --apple-purple-light: #bf5af2;
  --apple-purple-dark: #8e44ad;
  --apple-gray: #8e8e93;
  --apple-gray-light: #c7c7cc;
  --apple-gray-dark: #48484a;
  --apple-background: #f2f2f7;
  --apple-surface: #ffffff;
  --apple-text: #1d1d1f;
  --apple-text-secondary: #86868b;
  --apple-radius: 24rpx;
  --apple-radius-small: 16rpx;
  --apple-radius-large: 32rpx;
  --apple-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  --apple-shadow-elevated: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
  --apple-shadow-subtle: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.review-status-select {
  width: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// 标题区域
.select-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 6rpx;

  .title-text {
    font-size: 30rpx;
    font-weight: 600;
    color: var(--apple-text);
    letter-spacing: -0.6rpx;
  }

  .required-mark {
    color: var(--apple-red);
    font-size: 30rpx;
    font-weight: 600;
  }
}

// 状态选项容器
.status-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

// 状态选项
.status-option {
  position: relative;
  border-radius: var(--apple-radius);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  background: var(--apple-surface);
  box-shadow: var(--apple-shadow-subtle);

  &:active {
    transform: scale(0.98);
    box-shadow: var(--apple-shadow-elevated);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:active {
      transform: none;
      box-shadow: var(--apple-shadow-subtle);
    }
  }

  // 通过状态样式
  &.approved {
    &:hover {
      border-color: rgba(52, 199, 89, 0.3);
      box-shadow: 0 8rpx 40rpx rgba(52, 199, 89, 0.15);
    }

    &.selected {
      border-color: var(--apple-green);
      background: linear-gradient(135deg, rgba(52, 199, 89, 0.03) 0%, rgba(52, 199, 89, 0.08) 100%);
      box-shadow: 0 8rpx 40rpx rgba(52, 199, 89, 0.25);

      .option-icon-wrapper {
        background: rgba(52, 199, 89, 0.12);
      }

      .option-name {
        color: var(--apple-green);
        font-weight: 600;
      }
    }
  }

  // 拒绝状态样式
  &.rejected {
    &:hover {
      border-color: rgba(255, 59, 48, 0.3);
      box-shadow: 0 8rpx 40rpx rgba(255, 59, 48, 0.15);
    }

    &.selected {
      border-color: var(--apple-red);
      background: linear-gradient(135deg, rgba(255, 59, 48, 0.03) 0%, rgba(255, 59, 48, 0.08) 100%);
      box-shadow: 0 8rpx 40rpx rgba(255, 59, 48, 0.25);

      .option-icon-wrapper {
        background: rgba(255, 59, 48, 0.12);
      }

      .option-name {
        color: var(--apple-red);
        font-weight: 600;
      }
    }
  }

  // 等待状态样式
  &.none {
    &:hover {
      border-color: rgba(142, 142, 147, 0.3);
      box-shadow: 0 8rpx 40rpx rgba(142, 142, 147, 0.15);
    }

    &.selected {
      border-color: var(--apple-gray);
      background: linear-gradient(
        135deg,
        rgba(142, 142, 147, 0.03) 0%,
        rgba(142, 142, 147, 0.08) 100%
      );
      box-shadow: 0 8rpx 40rpx rgba(142, 142, 147, 0.25);

      .option-icon-wrapper {
        background: rgba(142, 142, 147, 0.12);
      }

      .option-name {
        color: var(--apple-gray);
        font-weight: 600;
      }
    }
  }
}

// 选项内容
.option-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  padding: 24rpx;
  gap: 20rpx;
}

// 图标包装器
.option-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--apple-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--apple-background);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex-shrink: 0;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

// 文本区域
.option-text {
  flex: 1;
  min-width: 0;
}

.option-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--apple-text);
  margin-bottom: 8rpx;
  letter-spacing: -0.6rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.option-desc {
  display: block;
  font-size: 24rpx;
  color: var(--apple-text-secondary);
  line-height: 1.4;
  font-weight: 400;
  letter-spacing: -0.2rpx;
}

// 选中指示器
.selected-indicator {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--apple-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  animation: scaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.2), rgba(90, 200, 250, 0.2));
    z-index: -1;
    animation: pulse 2s infinite;
  }
}

// 触摸反馈
.touch-feedback {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 122, 255, 0.08);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.status-option:active .touch-feedback {
  opacity: 1;
}

// 错误信息
.error-message {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 16rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 59, 48, 0.08);
  border-radius: var(--apple-radius-small);
  border: 2rpx solid rgba(255, 59, 48, 0.15);
  box-shadow: var(--apple-shadow-subtle);

  .error-text {
    font-size: 26rpx;
    color: var(--apple-red);
    font-weight: 500;
    letter-spacing: -0.2rpx;
  }
}

// 动画定义
@keyframes scaleIn {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotate(-90deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

// 尺寸变体
.review-status-select.size-small {
  .option-content {
    padding: 20rpx;
    gap: 16rpx;
  }

  .option-icon-wrapper {
    width: 64rpx;
    height: 64rpx;
  }

  .option-name {
    font-size: 28rpx;
  }

  .option-desc {
    font-size: 22rpx;
  }

  .selected-indicator {
    width: 40rpx;
    height: 40rpx;
  }
}

.review-status-select.size-large {
  .option-content {
    padding: 32rpx;
    gap: 24rpx;
  }

  .option-icon-wrapper {
    width: 96rpx;
    height: 96rpx;
  }

  .option-name {
    font-size: 36rpx;
  }

  .option-desc {
    font-size: 26rpx;
  }

  .selected-indicator {
    width: 56rpx;
    height: 56rpx;
  }
}
</style>
